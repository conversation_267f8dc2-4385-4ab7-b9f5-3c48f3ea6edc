<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTI Dashboard - Cyber Threat Intelligence</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/visual-enhancements.css" rel="stylesheet">
    <!-- Light Professional Theme CSS -->
    <link id="theme-css" href="css/themes/light-professional.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check"></i>
                CTI Dashboard
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard" data-section="dashboard">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#ioc" data-section="ioc">
                            <i class="bi bi-bug"></i> IoC Analysis
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#actor" data-section="actor">
                            <i class="bi bi-person-x"></i> Threat Actors
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#passive" data-section="passive">
                            <i class="bi bi-search"></i> Passive Scan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#watchlist" data-section="watchlist">
                            <i class="bi bi-eye"></i> Watchlist
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#settings" data-section="settings">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <span class="navbar-text" id="connection-status">
                            <i class="bi bi-circle-fill text-success"></i> Connected
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid main-content">
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="content-section active">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-speedometer2"></i> Dashboard Overview</h2>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-muted">Active Watchlist Items</h6>
                                    <div class="animated-counter" id="watchlist-count">-</div>
                                </div>
                                <div class="progress-ring">
                                    <svg>
                                        <circle class="bg-circle" cx="60" cy="60" r="45"></circle>
                                        <circle class="progress-circle" cx="60" cy="60" r="45" id="watchlist-progress"></circle>
                                    </svg>
                                    <div class="progress-text">
                                        <i class="bi bi-eye"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title text-muted">Pending Alerts</h6>
                                    <div class="animated-counter" id="alerts-count">-</div>
                                    <div class="threat-level medium" id="alert-threat-level">
                                        <span class="status-dot warning"></span>
                                        Medium Risk
                                    </div>
                                </div>
                                <div class="stat-icon bg-warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">IoCs Processed</h6>
                                    <h3 class="mb-0" id="ioc-count">-</h3>
                                </div>
                                <div class="stat-icon bg-success">
                                    <i class="bi bi-bug"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">System Status</h6>
                                    <h3 class="mb-0" id="system-status">Healthy</h3>
                                </div>
                                <div class="stat-icon bg-info">
                                    <i class="bi bi-heart-pulse"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-clock-history"></i> Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div id="recent-activity">
                                <p class="text-muted">Loading recent activity...</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-exclamation-triangle"></i> Latest Alerts</h5>
                        </div>
                        <div class="card-body">
                            <div id="latest-alerts">
                                <p class="text-muted">Loading alerts...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- IoC Analysis Section -->
        <div id="ioc-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-bug"></i> IoC Analysis</h2>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Single IoC Analysis</h5>
                        </div>
                        <div class="card-body">
                            <form id="ioc-form">
                                <div class="mb-3">
                                    <label for="ioc-value" class="form-label">IoC Value</label>
                                    <input type="text" class="form-control" id="ioc-value" placeholder="Enter IP, domain, hash, etc." required>
                                </div>
                                <div class="mb-3">
                                    <label for="ioc-source" class="form-label">Source</label>
                                    <input type="text" class="form-control" id="ioc-source" placeholder="Source of the IoC" required>
                                </div>
                                <div class="mb-3">
                                    <label for="threat-actor" class="form-label">Threat Actor (Optional)</label>
                                    <input type="text" class="form-control" id="threat-actor" placeholder="Associated threat actor">
                                </div>
                                <div class="mb-3">
                                    <label for="malware-family" class="form-label">Malware Family (Optional)</label>
                                    <input type="text" class="form-control" id="malware-family" placeholder="Associated malware family">
                                </div>
                                <div class="mb-3">
                                    <label for="ioc-tags" class="form-label">Tags (Optional)</label>
                                    <input type="text" class="form-control" id="ioc-tags" placeholder="Comma-separated tags">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> Analyze IoC
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Analysis Results</h5>
                        </div>
                        <div class="card-body">
                            <div id="ioc-results">
                                <p class="text-muted">Submit an IoC for analysis to see results here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Threat Actor Section -->
        <div id="actor-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-person-x"></i> Threat Actor Analysis</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>Threat Actor Information</h5>
                        </div>
                        <div class="card-body">
                            <form id="actor-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="actor-name" class="form-label">Actor Name</label>
                                            <input type="text" class="form-control" id="actor-name" placeholder="e.g., APT29, Lazarus Group" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="actor-aliases" class="form-label">Aliases</label>
                                            <input type="text" class="form-control" id="actor-aliases" placeholder="Comma-separated aliases">
                                        </div>
                                        <div class="mb-3">
                                            <label for="actor-origin" class="form-label">Origin Country</label>
                                            <input type="text" class="form-control" id="actor-origin" placeholder="e.g., Russia, North Korea">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="actor-motivation" class="form-label">Motivation</label>
                                            <input type="text" class="form-control" id="actor-motivation" placeholder="e.g., espionage, financial">
                                        </div>
                                        <div class="mb-3">
                                            <label for="actor-industries" class="form-label">Target Industries</label>
                                            <input type="text" class="form-control" id="actor-industries" placeholder="e.g., government, healthcare">
                                        </div>
                                        <div class="mb-3">
                                            <label for="actor-regions" class="form-label">Target Regions</label>
                                            <input type="text" class="form-control" id="actor-regions" placeholder="e.g., North America, Europe">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="actor-description" class="form-label">Description</label>
                                    <textarea class="form-control" id="actor-description" rows="3" placeholder="Brief description of the threat actor" required></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="actor-ttps" class="form-label">TTPs (Tactics, Techniques, Procedures)</label>
                                            <textarea class="form-control" id="actor-ttps" rows="3" placeholder="Comma-separated TTPs"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="actor-malware" class="form-label">Associated Malware</label>
                                            <textarea class="form-control" id="actor-malware" rows="3" placeholder="Comma-separated malware families"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-cpu"></i> Analyze Actor
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="dashboard.generateActorReport()">
                                        <i class="bi bi-file-text"></i> Generate Report
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Analysis Results</h5>
                        </div>
                        <div class="card-body">
                            <div id="actor-results">
                                <p class="text-muted">Submit threat actor information for analysis.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Passive Scan Section -->
        <div id="passive-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-search"></i> Passive Scanning</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Scan Configuration</h5>
                        </div>
                        <div class="card-body">
                            <form id="passive-scan-form">
                                <div class="mb-3">
                                    <label for="scan-target" class="form-label">Target</label>
                                    <input type="text" class="form-control" id="scan-target"
                                           placeholder="IP address or domain" required>
                                    <div class="form-text">Enter an IP address or domain name to scan</div>
                                </div>
                                <div class="mb-3">
                                    <label for="scan-type" class="form-label">Scan Type</label>
                                    <select class="form-select" id="scan-type">
                                        <option value="auto">Auto-detect</option>
                                        <option value="ip">IP Address</option>
                                        <option value="domain">Domain</option>
                                        <option value="subnet">Subnet</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search"></i> Start Passive Scan
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6>Scan Sources</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="source-shodan" checked>
                                <label class="form-check-label" for="source-shodan">Shodan</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="source-censys" checked>
                                <label class="form-check-label" for="source-censys">Censys</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="source-zoomeye" checked>
                                <label class="form-check-label" for="source-zoomeye">ZoomEye</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>Scan Results</h5>
                        </div>
                        <div class="card-body">
                            <div id="passive-scan-results">
                                <p class="text-muted">Configure and start a passive scan to see results here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Watchlist Section -->
        <div id="watchlist-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-eye"></i> Watchlist Management</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Add Watchlist Item</h5>
                        </div>
                        <div class="card-body">
                            <form id="watchlist-form">
                                <div class="mb-3">
                                    <label for="watchlist-value" class="form-label">Value</label>
                                    <input type="text" class="form-control" id="watchlist-value"
                                           placeholder="IP, domain, hash, etc." required>
                                </div>
                                <div class="mb-3">
                                    <label for="watchlist-type" class="form-label">Type</label>
                                    <select class="form-select" id="watchlist-type" required>
                                        <option value="">Select type...</option>
                                        <option value="ip">IP Address</option>
                                        <option value="domain">Domain</option>
                                        <option value="hash">File Hash</option>
                                        <option value="email">Email</option>
                                        <option value="url">URL</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="watchlist-description" class="form-label">Description</label>
                                    <textarea class="form-control" id="watchlist-description"
                                              rows="3" placeholder="Why is this being monitored?" required></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="watchlist-severity" class="form-label">Severity</label>
                                    <select class="form-select" id="watchlist-severity">
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                        <option value="critical">Critical</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="watchlist-tags" class="form-label">Tags</label>
                                    <input type="text" class="form-control" id="watchlist-tags"
                                           placeholder="Comma-separated tags">
                                </div>
                                <div class="mb-3">
                                    <label for="watchlist-expiry" class="form-label">Expiry (days)</label>
                                    <input type="number" class="form-control" id="watchlist-expiry"
                                           placeholder="Leave empty for no expiry">
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-plus-circle"></i> Add to Watchlist
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>Active Watchlist Items</h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="dashboard.refreshWatchlist()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="watchlist-items">
                                <p class="text-muted">Loading watchlist items...</p>
                            </div>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h5>Recent Alerts</h5>
                        </div>
                        <div class="card-body">
                            <div id="watchlist-alerts">
                                <p class="text-muted">Loading alerts...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div id="settings-section" class="content-section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-gear"></i> API Configuration</h2>
                </div>
            </div>
            <!-- Content will be added in next iteration -->
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Processing...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/app.js"></script>
</body>
</html>

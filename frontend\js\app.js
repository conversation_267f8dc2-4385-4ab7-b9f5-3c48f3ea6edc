/**
 * CTI Dashboard Frontend Application
 */

class CTIDashboard {
    constructor() {
        console.log('CTI Dashboard initializing...'); // Debug log
        this.apiBaseUrl = 'http://127.0.0.1:8000';
        this.apiKeys = this.loadApiKeys();
        this.currentSection = 'dashboard';

        // Initialize themes first
        this.themes = {
            'light-professional': {
                name: 'Light Professional',
                file: 'css/themes/light-professional.css',
                icon: 'bi-moon-fill'
            },
            'dark-professional': {
                name: 'Dark Professional',
                file: 'css/themes/dark-professional.css',
                icon: 'bi-sun-fill'
            }
        };

        // Then load theme preference
        this.currentTheme = this.loadThemePreference();

        this.init();
    }

    init() {
        console.log('CTI Dashboard init() called'); // Debug log
        // Since we're already called from DOMContentLoaded, we can set up everything directly
        this.setupEventListeners();
        this.initializeTheme();
        this.checkBackendConnection();
        this.loadDashboardDataEnhanced();
        this.setupNavigation();
        this.startRealTimeUpdates();
        this.simulateWebSocketUpdates();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('[data-section]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSection(e.target.dataset.section);
            });
        });

        // IoC Form
        const iocForm = document.getElementById('ioc-form');
        if (iocForm) {
            iocForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.analyzeIoC();
            });
        }

        // Threat Actor Form
        const actorForm = document.getElementById('actor-form');
        if (actorForm) {
            actorForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.analyzeThreatActor();
            });
        }

        // Passive Scan Form
        const passiveScanForm = document.getElementById('passive-scan-form');
        if (passiveScanForm) {
            passiveScanForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startPassiveScan();
            });
        }

        // Watchlist Form
        const watchlistForm = document.getElementById('watchlist-form');
        if (watchlistForm) {
            watchlistForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.addWatchlistItem();
            });
        }

        // Theme Toggle Button
        const themeToggle = document.getElementById('theme-toggle');
        console.log('Theme toggle button found:', themeToggle); // Debug log
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                console.log('Theme toggle button clicked!'); // Debug log
                this.toggleTheme();
            });
            console.log('Theme toggle event listener attached'); // Debug log
        } else {
            console.error('Theme toggle button not found!');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.stopRealTimeUpdates();
        });
    }

    setupNavigation() {
        // Set active navigation item
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-section="${this.currentSection}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
            this.setupNavigation();

            // Load section-specific data
            switch (sectionName) {
                case 'dashboard':
                    this.loadDashboardDataEnhanced();
                    break;
                case 'watchlist':
                    this.loadWatchlistData();
                    break;
                case 'settings':
                    this.loadSettingsSection();
                    break;
            }
        }
    }

    async checkBackendConnection() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            const data = await response.json();
            
            const statusElement = document.getElementById('connection-status');
            if (response.ok && data.status === 'healthy') {
                statusElement.innerHTML = '<i class="bi bi-circle-fill text-success"></i> Connected';
            } else {
                statusElement.innerHTML = '<i class="bi bi-circle-fill text-warning"></i> Degraded';
            }
        } catch (error) {
            console.error('Backend connection error:', error);
            const statusElement = document.getElementById('connection-status');
            statusElement.innerHTML = '<i class="bi bi-circle-fill text-danger"></i> Disconnected';
        }
    }

    async loadDashboardData() {
        try {
            // Load watchlist stats
            const watchlistResponse = await fetch(`${this.apiBaseUrl}/watchlist/stats`);
            if (watchlistResponse.ok) {
                const watchlistData = await watchlistResponse.json();
                if (watchlistData.success) {
                    document.getElementById('watchlist-count').textContent = 
                        watchlistData.statistics.total_items || 0;
                }
            }

            // Load alerts
            const alertsResponse = await fetch(`${this.apiBaseUrl}/watchlist/alerts?acknowledged=false&limit=5`);
            if (alertsResponse.ok) {
                const alertsData = await alertsResponse.json();
                if (alertsData.success) {
                    document.getElementById('alerts-count').textContent = alertsData.total_alerts || 0;
                    this.displayLatestAlerts(alertsData.alerts || []);
                }
            }

            // Update system status
            const healthResponse = await fetch(`${this.apiBaseUrl}/health`);
            if (healthResponse.ok) {
                const healthData = await healthResponse.json();
                document.getElementById('system-status').textContent = 
                    healthData.status === 'healthy' ? 'Healthy' : 'Degraded';
            }

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    displayLatestAlerts(alerts) {
        const alertsContainer = document.getElementById('latest-alerts');
        
        if (alerts.length === 0) {
            alertsContainer.innerHTML = '<p class="text-muted">No recent alerts</p>';
            return;
        }

        const alertsHtml = alerts.map(alert => `
            <div class="alert alert-${this.getSeverityClass(alert.severity)} alert-dismissible fade show" role="alert">
                <strong>${alert.severity.toUpperCase()}</strong>
                <br>
                <small>${alert.matched_value}</small>
                <br>
                <small class="text-muted">${new Date(alert.timestamp).toLocaleString()}</small>
            </div>
        `).join('');

        alertsContainer.innerHTML = alertsHtml;
    }

    getSeverityClass(severity) {
        const severityMap = {
            'low': 'info',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'danger'
        };
        return severityMap[severity.toLowerCase()] || 'info';
    }

    async analyzeIoC() {
        const form = document.getElementById('ioc-form');
        const resultsContainer = document.getElementById('ioc-results');
        const submitButton = form.querySelector('button[type="submit"]');
        
        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Analyzing...';
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Analyzing IoC...</p></div>';

        try {
            const formData = new FormData(form);
            const iocData = {
                value: document.getElementById('ioc-value').value,
                source: document.getElementById('ioc-source').value,
                threat_actor: document.getElementById('threat-actor').value || null,
                malware_family: document.getElementById('malware-family').value || null,
                tags: document.getElementById('ioc-tags').value ? 
                      document.getElementById('ioc-tags').value.split(',').map(tag => tag.trim()) : []
            };

            const response = await fetch(`${this.apiBaseUrl}/ioc/ingest`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(iocData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.displayIoCResults(result.ioc);
            } else {
                throw new Error(result.detail || 'Analysis failed');
            }

        } catch (error) {
            console.error('IoC analysis error:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Analysis Failed:</strong> ${error.message}
                </div>
            `;
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-search"></i> Analyze IoC';
        }
    }

    displayIoCResults(ioc) {
        const resultsContainer = document.getElementById('ioc-results');
        
        const confidenceClass = ioc.confidence >= 0.8 ? 'success' : 
                               ioc.confidence >= 0.5 ? 'warning' : 'danger';
        
        const tagsHtml = ioc.tags.map(tag => 
            `<span class="badge bg-secondary">${tag}</span>`
        ).join(' ');

        const enrichmentHtml = ioc.enrichment_sources.map(source => 
            `<span class="badge bg-info">${source}</span>`
        ).join(' ');

        resultsContainer.innerHTML = `
            <div class="result-item fade-in">
                <h6><i class="bi bi-bug"></i> IoC Analysis Results</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Value:</strong> ${ioc.value}</p>
                        <p><strong>Type:</strong> ${ioc.type}</p>
                        <p><strong>Confidence:</strong> 
                            <span class="badge bg-${confidenceClass}">${(ioc.confidence * 100).toFixed(1)}%</span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Threat Actor:</strong> ${ioc.threat_actor || 'Unknown'}</p>
                        <p><strong>Malware Family:</strong> ${ioc.malware_family || 'Unknown'}</p>
                    </div>
                </div>
                ${tagsHtml ? `<p><strong>Tags:</strong> ${tagsHtml}</p>` : ''}
                ${enrichmentHtml ? `<p><strong>Enrichment Sources:</strong> ${enrichmentHtml}</p>` : ''}
            </div>
        `;
    }

    // API Key Management
    loadApiKeys() {
        const stored = localStorage.getItem('cti_api_keys');
        return stored ? JSON.parse(stored) : {
            gemini: '',
            deepseek: '',
            abuseipdb: '',
            virustotal: '',
            shodan: '',
            censys_id: '',
            censys_secret: '',
            zoomeye: ''
        };
    }

    saveApiKeys() {
        localStorage.setItem('cti_api_keys', JSON.stringify(this.apiKeys));
    }

    loadSettingsSection() {
        const settingsSection = document.getElementById('settings-section');
        
        settingsSection.innerHTML = `
            <div class="row">
                <div class="col-12">
                    <h2><i class="bi bi-gear"></i> API Configuration</h2>
                    <p class="text-muted">Configure your API keys for various threat intelligence services. Keys are stored locally in your browser.</p>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="api-config-section">
                        <h6><i class="bi bi-robot"></i> AI Services</h6>
                        <div class="mb-3">
                            <label for="gemini-key" class="form-label">Google Gemini API Key</label>
                            <input type="password" class="form-control" id="gemini-key" 
                                   value="${this.apiKeys.gemini}" placeholder="Enter Gemini API key">
                        </div>
                        <div class="mb-3">
                            <label for="deepseek-key" class="form-label">DeepSeek API Key</label>
                            <input type="password" class="form-control" id="deepseek-key" 
                                   value="${this.apiKeys.deepseek}" placeholder="Enter DeepSeek API key">
                        </div>
                    </div>
                    
                    <div class="api-config-section">
                        <h6><i class="bi bi-shield-check"></i> Threat Intelligence</h6>
                        <div class="mb-3">
                            <label for="abuseipdb-key" class="form-label">AbuseIPDB API Key</label>
                            <input type="password" class="form-control" id="abuseipdb-key" 
                                   value="${this.apiKeys.abuseipdb}" placeholder="Enter AbuseIPDB API key">
                        </div>
                        <div class="mb-3">
                            <label for="virustotal-key" class="form-label">VirusTotal API Key</label>
                            <input type="password" class="form-control" id="virustotal-key" 
                                   value="${this.apiKeys.virustotal}" placeholder="Enter VirusTotal API key">
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="api-config-section">
                        <h6><i class="bi bi-search"></i> Search Engines</h6>
                        <div class="mb-3">
                            <label for="shodan-key" class="form-label">Shodan API Key</label>
                            <input type="password" class="form-control" id="shodan-key" 
                                   value="${this.apiKeys.shodan}" placeholder="Enter Shodan API key">
                        </div>
                        <div class="mb-3">
                            <label for="censys-id" class="form-label">Censys API ID</label>
                            <input type="text" class="form-control" id="censys-id" 
                                   value="${this.apiKeys.censys_id}" placeholder="Enter Censys API ID">
                        </div>
                        <div class="mb-3">
                            <label for="censys-secret" class="form-label">Censys API Secret</label>
                            <input type="password" class="form-control" id="censys-secret" 
                                   value="${this.apiKeys.censys_secret}" placeholder="Enter Censys API Secret">
                        </div>
                        <div class="mb-3">
                            <label for="zoomeye-key" class="form-label">ZoomEye API Key</label>
                            <input type="password" class="form-control" id="zoomeye-key" 
                                   value="${this.apiKeys.zoomeye}" placeholder="Enter ZoomEye API key">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" onclick="dashboard.saveApiConfiguration()">
                            <i class="bi bi-check-circle"></i> Save Configuration
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="dashboard.testApiConnections()">
                            <i class="bi bi-wifi"></i> Test Connections
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="dashboard.clearApiKeys()">
                            <i class="bi bi-trash"></i> Clear All Keys
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    saveApiConfiguration() {
        // Update API keys from form
        this.apiKeys.gemini = document.getElementById('gemini-key').value;
        this.apiKeys.deepseek = document.getElementById('deepseek-key').value;
        this.apiKeys.abuseipdb = document.getElementById('abuseipdb-key').value;
        this.apiKeys.virustotal = document.getElementById('virustotal-key').value;
        this.apiKeys.shodan = document.getElementById('shodan-key').value;
        this.apiKeys.censys_id = document.getElementById('censys-id').value;
        this.apiKeys.censys_secret = document.getElementById('censys-secret').value;
        this.apiKeys.zoomeye = document.getElementById('zoomeye-key').value;

        // Save to localStorage
        this.saveApiKeys();

        // Show success message
        this.showNotification('API configuration saved successfully!', 'success');
    }

    clearApiKeys() {
        if (confirm('Are you sure you want to clear all API keys? This action cannot be undone.')) {
            this.apiKeys = {
                gemini: '',
                deepseek: '',
                abuseipdb: '',
                virustotal: '',
                shodan: '',
                censys_id: '',
                censys_secret: '',
                zoomeye: ''
            };
            this.saveApiKeys();
            this.loadSettingsSection();
            this.showNotification('All API keys cleared.', 'info');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 100px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Threat Actor Analysis
    async analyzeThreatActor() {
        const form = document.getElementById('actor-form');
        const resultsContainer = document.getElementById('actor-results');
        const submitButton = form.querySelector('button[type="submit"]');

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Analyzing...';
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Analyzing threat actor...</p></div>';

        try {
            const actorData = {
                name: document.getElementById('actor-name').value,
                aliases: document.getElementById('actor-aliases').value ?
                        document.getElementById('actor-aliases').value.split(',').map(a => a.trim()) : [],
                description: document.getElementById('actor-description').value,
                origin_country: document.getElementById('actor-origin').value || null,
                motivation: document.getElementById('actor-motivation').value ?
                           document.getElementById('actor-motivation').value.split(',').map(m => m.trim()) : [],
                target_industries: document.getElementById('actor-industries').value ?
                                  document.getElementById('actor-industries').value.split(',').map(i => i.trim()) : [],
                target_regions: document.getElementById('actor-regions').value ?
                               document.getElementById('actor-regions').value.split(',').map(r => r.trim()) : [],
                ttps: document.getElementById('actor-ttps').value ?
                     document.getElementById('actor-ttps').value.split(',').map(t => t.trim()) : [],
                associated_malware: document.getElementById('actor-malware').value ?
                                   document.getElementById('actor-malware').value.split(',').map(m => m.trim()) : []
            };

            const response = await fetch(`${this.apiBaseUrl}/actor/analyze`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(actorData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.displayActorResults(result);
            } else {
                throw new Error(result.detail || 'Analysis failed');
            }

        } catch (error) {
            console.error('Threat actor analysis error:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Analysis Failed:</strong> ${error.message}
                </div>
            `;
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-cpu"></i> Analyze Actor';
        }
    }

    displayActorResults(result) {
        const resultsContainer = document.getElementById('actor-results');
        const analysis = result.analysis;

        resultsContainer.innerHTML = `
            <div class="result-item fade-in">
                <h6><i class="bi bi-person-x"></i> ${result.actor.name}</h6>
                <div class="mb-3">
                    <strong>Confidence:</strong>
                    <span class="badge bg-${analysis.confidence_level >= 0.8 ? 'success' : analysis.confidence_level >= 0.5 ? 'warning' : 'danger'}">
                        ${(analysis.confidence_level * 100).toFixed(1)}%
                    </span>
                </div>
                <div class="mb-3">
                    <strong>Executive Summary:</strong>
                    <p class="text-truncate-2">${analysis.executive_summary}</p>
                </div>
                <div class="mb-3">
                    <strong>Risk Assessment:</strong>
                    <p class="text-truncate-2">${analysis.risk_assessment}</p>
                </div>
                <div class="mb-2">
                    <strong>Attack Vectors:</strong>
                    <ul class="list-unstyled">
                        ${analysis.attack_vectors.slice(0, 3).map(vector => `<li>• ${vector}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }

    async generateActorReport() {
        // Similar to analyzeThreatActor but calls the /actor/report endpoint
        this.showNotification('Report generation feature coming soon!', 'info');
    }

    // Passive Scanning
    async startPassiveScan() {
        const form = document.getElementById('passive-scan-form');
        const resultsContainer = document.getElementById('passive-scan-results');
        const submitButton = form.querySelector('button[type="submit"]');

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Scanning...';
        resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Performing passive scan...</p></div>';

        try {
            const scanData = {
                target: document.getElementById('scan-target').value,
                scan_type: document.getElementById('scan-type').value
            };

            const response = await fetch(`${this.apiBaseUrl}/passive/scan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(scanData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.displayPassiveScanResults(result);
            } else {
                throw new Error(result.detail || 'Scan failed');
            }

        } catch (error) {
            console.error('Passive scan error:', error);
            resultsContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Scan Failed:</strong> ${error.message}
                </div>
            `;
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-search"></i> Start Passive Scan';
        }
    }

    displayPassiveScanResults(result) {
        const resultsContainer = document.getElementById('passive-scan-results');
        const scanResults = result.scan_results;

        const servicesHtml = scanResults.services.map(service => `
            <tr>
                <td>${service.port || 'N/A'}</td>
                <td>${service.service || 'Unknown'}</td>
                <td>${service.version || 'N/A'}</td>
                <td><span class="badge bg-info">${service.source || 'Unknown'}</span></td>
            </tr>
        `).join('');

        const vulnerabilitiesHtml = scanResults.vulnerabilities.map(vuln => `
            <div class="alert alert-warning" role="alert">
                <strong>${vuln.cve || 'Unknown CVE'}</strong><br>
                <small>${vuln.description || 'No description available'}</small>
            </div>
        `).join('');

        resultsContainer.innerHTML = `
            <div class="result-item fade-in">
                <h6><i class="bi bi-search"></i> Scan Results for ${result.target}</h6>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">${scanResults.total_sources}</h4>
                            <small class="text-muted">Sources</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">${scanResults.services_found}</h4>
                            <small class="text-muted">Services</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">${scanResults.vulnerabilities_found}</h4>
                            <small class="text-muted">Vulnerabilities</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">${scanResults.open_ports.length}</h4>
                            <small class="text-muted">Open Ports</small>
                        </div>
                    </div>
                </div>

                ${scanResults.open_ports.length > 0 ? `
                    <div class="mb-3">
                        <strong>Open Ports:</strong>
                        <div class="mt-2">
                            ${scanResults.open_ports.map(port => `<span class="badge bg-secondary me-1">${port}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}

                ${scanResults.services.length > 0 ? `
                    <div class="mb-3">
                        <strong>Services:</strong>
                        <div class="table-responsive mt-2">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Port</th>
                                        <th>Service</th>
                                        <th>Version</th>
                                        <th>Source</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${servicesHtml}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ` : ''}

                ${scanResults.vulnerabilities.length > 0 ? `
                    <div class="mb-3">
                        <strong>Vulnerabilities:</strong>
                        <div class="mt-2">
                            ${vulnerabilitiesHtml}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Watchlist Management
    async addWatchlistItem() {
        const form = document.getElementById('watchlist-form');
        const submitButton = form.querySelector('button[type="submit"]');

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Adding...';

        try {
            const watchlistData = {
                value: document.getElementById('watchlist-value').value,
                item_type: document.getElementById('watchlist-type').value,
                description: document.getElementById('watchlist-description').value,
                severity: document.getElementById('watchlist-severity').value,
                tags: document.getElementById('watchlist-tags').value ?
                     document.getElementById('watchlist-tags').value.split(',').map(tag => tag.trim()) : [],
                expiry_days: document.getElementById('watchlist-expiry').value ?
                            parseInt(document.getElementById('watchlist-expiry').value) : null
            };

            const response = await fetch(`${this.apiBaseUrl}/watchlist/add`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(watchlistData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showNotification('Item added to watchlist successfully!', 'success');
                form.reset();
                this.loadWatchlistData();
            } else {
                throw new Error(result.detail || 'Failed to add item');
            }

        } catch (error) {
            console.error('Watchlist add error:', error);
            this.showNotification(`Failed to add item: ${error.message}`, 'danger');
        } finally {
            // Reset button state
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="bi bi-plus-circle"></i> Add to Watchlist';
        }
    }

    async loadWatchlistData() {
        try {
            // Load watchlist items
            const itemsResponse = await fetch(`${this.apiBaseUrl}/watchlist/items`);
            if (itemsResponse.ok) {
                const itemsData = await itemsResponse.json();
                if (itemsData.success) {
                    this.displayWatchlistItems(itemsData.items);
                }
            }

            // Load alerts
            const alertsResponse = await fetch(`${this.apiBaseUrl}/watchlist/alerts?limit=10`);
            if (alertsResponse.ok) {
                const alertsData = await alertsResponse.json();
                if (alertsData.success) {
                    this.displayWatchlistAlerts(alertsData.alerts);
                }
            }

        } catch (error) {
            console.error('Error loading watchlist data:', error);
        }
    }

    displayWatchlistItems(items) {
        const container = document.getElementById('watchlist-items');

        if (items.length === 0) {
            container.innerHTML = '<p class="text-muted">No watchlist items found.</p>';
            return;
        }

        const itemsHtml = items.map(item => `
            <div class="card mb-2">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title">${item.value}</h6>
                            <p class="card-text text-muted small">${item.description}</p>
                            <div>
                                <span class="badge bg-${this.getSeverityClass(item.severity)}">${item.severity}</span>
                                <span class="badge bg-secondary">${item.type}</span>
                                ${item.tags.map(tag => `<span class="badge bg-light text-dark">${tag}</span>`).join(' ')}
                            </div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">Matches: ${item.match_count}</small><br>
                            <button class="btn btn-sm btn-outline-danger" onclick="dashboard.removeWatchlistItem('${item.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = itemsHtml;
    }

    displayWatchlistAlerts(alerts) {
        const container = document.getElementById('watchlist-alerts');

        if (alerts.length === 0) {
            container.innerHTML = '<p class="text-muted">No recent alerts.</p>';
            return;
        }

        const alertsHtml = alerts.map(alert => `
            <div class="alert alert-${this.getSeverityClass(alert.severity)} alert-dismissible fade show" role="alert">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>${alert.matched_value}</strong><br>
                        <small>Match type: ${alert.match_type} | Source: ${alert.source}</small><br>
                        <small class="text-muted">${new Date(alert.timestamp).toLocaleString()}</small>
                    </div>
                    <div>
                        ${!alert.acknowledged ? `
                            <button class="btn btn-sm btn-outline-primary" onclick="dashboard.acknowledgeAlert('${alert.id}')">
                                Acknowledge
                            </button>
                        ` : '<small class="text-muted">Acknowledged</small>'}
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = alertsHtml;
    }

    async removeWatchlistItem(itemId) {
        if (!confirm('Are you sure you want to remove this item from the watchlist?')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/watchlist/${itemId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showNotification('Item removed from watchlist', 'success');
                this.loadWatchlistData();
            } else {
                throw new Error(result.detail || 'Failed to remove item');
            }

        } catch (error) {
            console.error('Remove watchlist item error:', error);
            this.showNotification(`Failed to remove item: ${error.message}`, 'danger');
        }
    }

    async acknowledgeAlert(alertId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/watchlist/alerts/${alertId}/acknowledge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    acknowledged_by: 'user',
                    notes: 'Acknowledged via web interface'
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showNotification('Alert acknowledged', 'success');
                this.loadWatchlistData();
            } else {
                throw new Error(result.detail || 'Failed to acknowledge alert');
            }

        } catch (error) {
            console.error('Acknowledge alert error:', error);
            this.showNotification(`Failed to acknowledge alert: ${error.message}`, 'danger');
        }
    }

    refreshWatchlist() {
        this.loadWatchlistData();
        this.showNotification('Watchlist refreshed', 'info');
    }

    // API Testing
    async testApiConnections() {
        const testButton = document.querySelector('button[onclick="dashboard.testApiConnections()"]');
        testButton.disabled = true;
        testButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Testing...';

        const results = {};

        // Test backend connection first
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            results.backend = response.ok ? 'success' : 'warning';
        } catch (error) {
            results.backend = 'danger';
        }

        // Test API keys by checking if they're configured
        results.gemini = this.apiKeys.gemini ? 'info' : 'secondary';
        results.deepseek = this.apiKeys.deepseek ? 'info' : 'secondary';
        results.abuseipdb = this.apiKeys.abuseipdb ? 'info' : 'secondary';
        results.virustotal = this.apiKeys.virustotal ? 'info' : 'secondary';
        results.shodan = this.apiKeys.shodan ? 'info' : 'secondary';
        results.censys = (this.apiKeys.censys_id && this.apiKeys.censys_secret) ? 'info' : 'secondary';
        results.zoomeye = this.apiKeys.zoomeye ? 'info' : 'secondary';

        // Display results
        const resultsHtml = `
            <div class="mt-4">
                <h6>Connection Test Results</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Backend API
                                <span class="badge bg-${results.backend}">
                                    ${results.backend === 'success' ? 'Connected' : results.backend === 'warning' ? 'Issues' : 'Failed'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Gemini API
                                <span class="badge bg-${results.gemini}">
                                    ${results.gemini === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                DeepSeek API
                                <span class="badge bg-${results.deepseek}">
                                    ${results.deepseek === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                AbuseIPDB API
                                <span class="badge bg-${results.abuseipdb}">
                                    ${results.abuseipdb === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                VirusTotal API
                                <span class="badge bg-${results.virustotal}">
                                    ${results.virustotal === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Shodan API
                                <span class="badge bg-${results.shodan}">
                                    ${results.shodan === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Censys API
                                <span class="badge bg-${results.censys}">
                                    ${results.censys === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                ZoomEye API
                                <span class="badge bg-${results.zoomeye}">
                                    ${results.zoomeye === 'info' ? 'Configured' : 'Not Set'}
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        `;

        // Add results to settings section
        const settingsSection = document.getElementById('settings-section');
        const existingResults = settingsSection.querySelector('.test-results');
        if (existingResults) {
            existingResults.remove();
        }

        const resultsDiv = document.createElement('div');
        resultsDiv.className = 'test-results';
        resultsDiv.innerHTML = resultsHtml;
        settingsSection.appendChild(resultsDiv);

        // Reset button
        testButton.disabled = false;
        testButton.innerHTML = '<i class="bi bi-wifi"></i> Test Connections';

        this.showNotification('Connection test completed', 'info');
    }

    // Utility Methods
    async makeApiRequest(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const requestOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers,
            },
        };

        try {
            const response = await fetch(`${this.apiBaseUrl}${endpoint}`, requestOptions);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('Unable to connect to backend. Please check if the server is running.');
            }
            throw error;
        }
    }

    // Enhanced error handling
    handleApiError(error, context = '') {
        console.error(`API Error ${context}:`, error);

        let userMessage = error.message;

        // Provide user-friendly error messages
        if (error.message.includes('Unable to connect')) {
            userMessage = 'Cannot connect to the backend server. Please ensure it is running on http://127.0.0.1:8000';
        } else if (error.message.includes('CORS')) {
            userMessage = 'Cross-origin request blocked. Please check CORS configuration on the backend.';
        } else if (error.message.includes('401')) {
            userMessage = 'Authentication required. Please check your API keys.';
        } else if (error.message.includes('403')) {
            userMessage = 'Access forbidden. Please verify your permissions.';
        } else if (error.message.includes('429')) {
            userMessage = 'Rate limit exceeded. Please wait before making more requests.';
        } else if (error.message.includes('500')) {
            userMessage = 'Server error occurred. Please try again later.';
        }

        this.showNotification(userMessage, 'danger');
        return userMessage;
    }

    // Auto-retry mechanism for failed requests
    async retryApiRequest(requestFn, maxRetries = 3, delay = 1000) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await requestFn();
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }

                console.warn(`Request attempt ${attempt} failed, retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 2; // Exponential backoff
            }
        }
    }

    // Batch operations
    async batchIngestIoCs(iocs) {
        try {
            const response = await this.makeApiRequest('/ioc/batch', {
                method: 'POST',
                body: JSON.stringify({ iocs })
            });

            if (response.success) {
                this.showNotification(`Successfully processed ${response.processed} IoCs`, 'success');
                return response;
            } else {
                throw new Error('Batch ingestion failed');
            }
        } catch (error) {
            this.handleApiError(error, 'batch IoC ingestion');
            throw error;
        }
    }

    // System configuration management
    async updateSystemConfig(config) {
        try {
            const response = await this.makeApiRequest('/system/config', {
                method: 'POST',
                body: JSON.stringify(config)
            });

            if (response.success) {
                this.showNotification('System configuration updated', 'success');
                return response;
            } else {
                throw new Error('Configuration update failed');
            }
        } catch (error) {
            this.handleApiError(error, 'system configuration update');
            throw error;
        }
    }

    async getSystemConfig() {
        try {
            const response = await this.makeApiRequest('/system/config');
            return response.config;
        } catch (error) {
            this.handleApiError(error, 'getting system configuration');
            return {};
        }
    }

    // Enhanced loading states
    setLoadingState(elementId, isLoading, loadingText = 'Loading...') {
        const element = document.getElementById(elementId);
        if (!element) return;

        if (isLoading) {
            element.classList.add('loading');
            const originalContent = element.innerHTML;
            element.dataset.originalContent = originalContent;
            element.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">${loadingText}</span>
                </div>
            `;
        } else {
            element.classList.remove('loading');
            if (element.dataset.originalContent) {
                element.innerHTML = element.dataset.originalContent;
                delete element.dataset.originalContent;
            }
        }
    }

    // Data export functionality
    exportData(data, filename, format = 'json') {
        let content, mimeType;

        switch (format.toLowerCase()) {
            case 'json':
                content = JSON.stringify(data, null, 2);
                mimeType = 'application/json';
                break;
            case 'csv':
                content = this.convertToCSV(data);
                mimeType = 'text/csv';
                break;
            default:
                throw new Error('Unsupported export format');
        }

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename}.${format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification(`Data exported as ${filename}.${format}`, 'success');
    }

    convertToCSV(data) {
        if (!Array.isArray(data) || data.length === 0) {
            return '';
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row =>
                headers.map(header => {
                    const value = row[header];
                    // Escape commas and quotes in CSV
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');

        return csvContent;
    }

    // Real-time Features
    startRealTimeUpdates() {
        // Update dashboard every 30 seconds
        this.dashboardUpdateInterval = setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboardData();
            }
        }, 30000);

        // Update watchlist alerts every 15 seconds
        this.alertsUpdateInterval = setInterval(() => {
            if (this.currentSection === 'watchlist' || this.currentSection === 'dashboard') {
                this.updateAlertsRealTime();
            }
        }, 15000);

        // Update system status every 60 seconds
        this.statusUpdateInterval = setInterval(() => {
            this.checkBackendConnection();
        }, 60000);

        console.log('Real-time updates started');
    }

    stopRealTimeUpdates() {
        if (this.dashboardUpdateInterval) {
            clearInterval(this.dashboardUpdateInterval);
        }
        if (this.alertsUpdateInterval) {
            clearInterval(this.alertsUpdateInterval);
        }
        if (this.statusUpdateInterval) {
            clearInterval(this.statusUpdateInterval);
        }
        console.log('Real-time updates stopped');
    }

    async updateAlertsRealTime() {
        try {
            const response = await this.makeApiRequest('/watchlist/alerts?acknowledged=false&limit=10');

            if (response.success) {
                // Update alert count
                const alertCount = response.total_alerts || 0;
                const alertCountElement = document.getElementById('alerts-count');
                if (alertCountElement) {
                    const currentCount = parseInt(alertCountElement.textContent) || 0;
                    if (alertCount !== currentCount) {
                        alertCountElement.textContent = alertCount;

                        // Animate the change
                        alertCountElement.classList.add('text-warning');
                        setTimeout(() => {
                            alertCountElement.classList.remove('text-warning');
                        }, 2000);

                        // Show notification for new alerts
                        if (alertCount > currentCount) {
                            this.showNotification(`${alertCount - currentCount} new alert(s) detected!`, 'warning');
                            this.playNotificationSound();
                        }
                    }
                }

                // Update alerts display if on dashboard
                if (this.currentSection === 'dashboard') {
                    this.displayLatestAlerts(response.alerts || []);
                }

                // Update watchlist alerts if on watchlist page
                if (this.currentSection === 'watchlist') {
                    this.displayWatchlistAlerts(response.alerts || []);
                }
            }
        } catch (error) {
            console.warn('Failed to update alerts in real-time:', error);
        }
    }

    playNotificationSound() {
        // Create a simple notification sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }

    // Live Activity Feed
    addActivityItem(activity) {
        const activityContainer = document.getElementById('recent-activity');
        if (!activityContainer) return;

        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item fade-in mb-2';
        activityItem.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="activity-icon me-3">
                    <i class="bi bi-${activity.icon} text-${activity.type}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="activity-content">
                        <strong>${activity.title}</strong>
                        <p class="mb-1 text-muted">${activity.description}</p>
                        <small class="text-muted">${this.formatTimeAgo(activity.timestamp)}</small>
                    </div>
                </div>
            </div>
        `;

        // Add to top of activity feed
        activityContainer.insertBefore(activityItem, activityContainer.firstChild);

        // Remove old items (keep only last 10)
        const items = activityContainer.querySelectorAll('.activity-item');
        if (items.length > 10) {
            items[items.length - 1].remove();
        }
    }

    formatTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInSeconds = Math.floor((now - time) / 1000);

        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days > 1 ? 's' : ''} ago`;
        }
    }

    // WebSocket-like functionality simulation
    simulateWebSocketUpdates() {
        // Simulate receiving updates from the backend
        const updateTypes = [
            {
                type: 'ioc_analyzed',
                icon: 'bug',
                title: 'IoC Analysis Complete',
                description: 'New IP address analyzed with high confidence',
                type: 'success'
            },
            {
                type: 'alert_triggered',
                icon: 'exclamation-triangle',
                title: 'Watchlist Alert',
                description: 'Suspicious domain detected in network traffic',
                type: 'warning'
            },
            {
                type: 'scan_completed',
                icon: 'search',
                title: 'Passive Scan Complete',
                description: 'Target scan finished, 5 services discovered',
                type: 'info'
            },
            {
                type: 'actor_updated',
                icon: 'person-x',
                title: 'Threat Actor Profile Updated',
                description: 'New TTPs added to existing actor profile',
                type: 'primary'
            }
        ];

        // Simulate random updates every 30-120 seconds
        const scheduleNextUpdate = () => {
            const delay = Math.random() * 90000 + 30000; // 30-120 seconds
            setTimeout(() => {
                const update = updateTypes[Math.floor(Math.random() * updateTypes.length)];
                update.timestamp = new Date().toISOString();
                this.addActivityItem(update);
                scheduleNextUpdate();
            }, delay);
        };

        scheduleNextUpdate();
    }

    // Enhanced notification system
    showEnhancedNotification(title, message, type = 'info', actions = []) {
        const notificationId = 'notification-' + Date.now();
        const notification = document.createElement('div');
        notification.id = notificationId;
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 100px; right: 20px; z-index: 1050; min-width: 350px; max-width: 400px;';

        const actionsHtml = actions.map(action =>
            `<button type="button" class="btn btn-sm btn-outline-${type} me-2" onclick="${action.onclick}">${action.text}</button>`
        ).join('');

        notification.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="me-3">
                    <i class="bi bi-${this.getNotificationIcon(type)} fs-4"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="alert-heading mb-1">${title}</h6>
                    <p class="mb-2">${message}</p>
                    ${actionsHtml ? `<div class="mb-2">${actionsHtml}</div>` : ''}
                    <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds if no actions
        if (actions.length === 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 10000);
        }

        return notificationId;
    }

    getNotificationIcon(type) {
        const iconMap = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'x-circle',
            'info': 'info-circle',
            'primary': 'bell'
        };
        return iconMap[type] || 'bell';
    }

    // Enhanced Theme Management System
    loadThemePreference() {
        const savedTheme = localStorage.getItem('cti-dashboard-theme');
        // Check if saved theme exists and is valid
        if (savedTheme && this.themes && this.themes[savedTheme]) {
            return savedTheme;
        }
        return 'light-professional';
    }

    saveThemePreference(theme) {
        localStorage.setItem('cti-dashboard-theme', theme);
    }

    initializeTheme() {
        this.applyTheme(this.currentTheme);
        this.updateThemeToggleIcon();
    }

    applyTheme(themeName) {
        console.log('Applying theme:', themeName); // Debug log

        if (!this.themes[themeName]) {
            console.warn(`Theme ${themeName} not found, falling back to light-professional`);
            themeName = 'light-professional';
        }

        const themeLink = document.getElementById('theme-css');
        if (!themeLink) {
            console.error('Theme CSS link element not found!');
            return;
        }

        const theme = this.themes[themeName];
        console.log('Theme config:', theme); // Debug log

        // Add transition class for smooth theme switching
        document.body.classList.add('theme-transitioning');

        // Apply the theme
        console.log('Setting theme CSS href to:', theme.file); // Debug log
        themeLink.href = theme.file;

        // Clear existing theme classes and apply new one
        document.body.className = document.body.className.replace(/theme-\w+-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`, 'theme-transitioning');

        console.log('Applied body classes:', document.body.className); // Debug log
        console.log('Theme CSS link href is now:', themeLink.href); // Debug log

        // Remove transition class after animation
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, 300);

        this.currentTheme = themeName;
        this.saveThemePreference(themeName);
        this.updateThemeToggleIcon();
    }

    toggleTheme() {
        console.log('Toggle theme clicked, current theme:', this.currentTheme); // Debug log
        const newTheme = this.currentTheme === 'light-professional' ? 'dark-professional' : 'light-professional';
        console.log('Switching to theme:', newTheme); // Debug log
        this.applyTheme(newTheme);

        // Add a subtle animation to the toggle button
        const toggleButton = document.getElementById('theme-toggle');
        if (toggleButton) {
            toggleButton.style.transform = 'scale(0.95)';
            setTimeout(() => {
                toggleButton.style.transform = '';
            }, 150);
        }
    }

    updateThemeToggleIcon() {
        const themeIcon = document.getElementById('theme-icon');
        const toggleButton = document.getElementById('theme-toggle');

        if (themeIcon && toggleButton) {
            const theme = this.themes[this.currentTheme];
            themeIcon.className = `bi ${theme.icon}`;
            toggleButton.title = `Switch to ${this.currentTheme === 'light-professional' ? 'Dark' : 'Light'} Theme`;
        }
    }



    // Visual Enhancement Functions
    animateCounter(elementId, targetValue, duration = 1000) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = parseInt(element.textContent) || 0;
        const increment = (targetValue - startValue) / (duration / 16);
        let currentValue = startValue;

        element.classList.add('counting');

        const timer = setInterval(() => {
            currentValue += increment;
            if ((increment > 0 && currentValue >= targetValue) ||
                (increment < 0 && currentValue <= targetValue)) {
                currentValue = targetValue;
                clearInterval(timer);
                element.classList.remove('counting');
            }
            element.textContent = Math.floor(currentValue);
        }, 16);
    }

    updateProgressRing(elementId, percentage) {
        const circle = document.getElementById(elementId);
        if (!circle) return;

        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;
        const offset = circumference - (percentage / 100) * circumference;

        circle.style.strokeDasharray = `${circumference} ${circumference}`;
        circle.style.strokeDashoffset = offset;
    }

    createHeatmap(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        container.className = 'heatmap-grid';

        data.forEach((value, index) => {
            const cell = document.createElement('div');
            cell.className = `heatmap-cell intensity-${Math.min(4, Math.floor(value * 5))}`;
            cell.title = `Value: ${value}`;
            cell.addEventListener('click', () => {
                this.showNotification(`Heatmap cell ${index}: ${value}`, 'info');
            });
            container.appendChild(cell);
        });
    }

    createTimelineChart(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        container.className = 'timeline-chart';

        const maxValue = Math.max(...data);

        data.forEach((value, index) => {
            const bar = document.createElement('div');
            bar.className = 'timeline-bar';
            if (value > maxValue * 0.8) {
                bar.classList.add('high-activity');
            }

            const height = (value / maxValue) * 180;
            bar.style.height = `${height}px`;
            bar.style.left = `${(index / data.length) * 100}%`;
            bar.title = `Time ${index}: ${value} events`;

            container.appendChild(bar);
        });
    }

    createNetworkTopology(containerId, nodes, connections) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';
        container.style.position = 'relative';
        container.style.height = '300px';

        // Create nodes
        nodes.forEach((node, index) => {
            const nodeElement = document.createElement('div');
            nodeElement.className = `network-node ${node.status || ''}`;
            nodeElement.textContent = node.label;
            nodeElement.style.position = 'absolute';
            nodeElement.style.left = `${node.x}%`;
            nodeElement.style.top = `${node.y}%`;
            nodeElement.title = `${node.label}: ${node.status || 'normal'}`;

            nodeElement.addEventListener('click', () => {
                this.showNotification(`Node: ${node.label} (${node.status || 'normal'})`, 'info');
            });

            container.appendChild(nodeElement);
        });

        // Create connections
        connections.forEach(connection => {
            const line = document.createElement('div');
            line.className = 'network-connection';

            const fromNode = nodes[connection.from];
            const toNode = nodes[connection.to];

            const deltaX = toNode.x - fromNode.x;
            const deltaY = toNode.y - fromNode.y;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;

            line.style.width = `${distance}%`;
            line.style.left = `${fromNode.x}%`;
            line.style.top = `${fromNode.y}%`;
            line.style.transform = `rotate(${angle}deg)`;

            container.appendChild(line);
        });
    }



    // Enhanced dashboard data loading with visual updates
    async loadDashboardDataEnhanced() {
        try {
            // Load regular dashboard data
            await this.loadDashboardData();

            // Add visual enhancements
            const watchlistCount = parseInt(document.getElementById('watchlist-count').textContent) || 0;
            const alertsCount = parseInt(document.getElementById('alerts-count').textContent) || 0;

            // Animate counters
            this.animateCounter('watchlist-count', watchlistCount);
            this.animateCounter('alerts-count', alertsCount);

            // Update progress ring for watchlist
            this.updateProgressRing('watchlist-progress', Math.min(100, (watchlistCount / 50) * 100));

            // Create sample heatmap data
            const heatmapData = Array.from({length: 100}, () => Math.random());

            // Create sample timeline data
            const timelineData = Array.from({length: 24}, () => Math.floor(Math.random() * 100));

            // Update threat level based on alerts
            const threatLevelElement = document.getElementById('alert-threat-level');
            if (threatLevelElement) {
                let level = 'low';
                let levelText = 'Low Risk';

                if (alertsCount > 10) {
                    level = 'high';
                    levelText = 'High Risk';
                } else if (alertsCount > 5) {
                    level = 'medium';
                    levelText = 'Medium Risk';
                }

                threatLevelElement.className = `threat-level ${level}`;
                threatLevelElement.innerHTML = `<span class="status-dot ${level === 'low' ? 'online' : level === 'medium' ? 'warning' : 'offline'}"></span>${levelText}`;
            }

        } catch (error) {
            console.error('Error loading enhanced dashboard data:', error);
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, creating CTI Dashboard...'); // Debug log
    window.dashboard = new CTIDashboard();

    // Add global test function for debugging
    window.testThemeToggle = function() {
        console.log('Manual theme toggle test');
        if (window.dashboard) {
            window.dashboard.toggleTheme();
        } else {
            console.error('Dashboard not initialized');
        }
    };
});
